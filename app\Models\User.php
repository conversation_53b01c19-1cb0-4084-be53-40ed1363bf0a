<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'student_id',
        'phone',
        'bio',
        'avatar',
        'background_photo',
        'notification_preferences',
        'notifications_enabled',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'notification_preferences' => 'array',
            'notifications_enabled' => 'boolean',
        ];
    }

    /**
     * Check if user is a student
     */
    public function isStudent(): bool
    {
        return $this->role === 'student';
    }

    /**
     * Check if user is an organization officer
     */
    public function isOrgOfficer(): bool
    {
        return $this->role === 'org_officer';
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user has admin or org officer privileges
     */
    public function hasManagementAccess(): bool
    {
        return in_array($this->role, ['admin', 'org_officer']);
    }

    /**
     * Get organizations this user belongs to
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_members')
            ->using(OrganizationMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active organizations this user belongs to
     */
    public function activeOrganizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_members')
            ->using(OrganizationMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps()
            ->wherePivot('status', 'active')
            ->where('organizations.status', 'active');
    }

    /**
     * Get organizations this user created
     */
    public function createdOrganizations(): HasMany
    {
        return $this->hasMany(Organization::class, 'created_by');
    }

    /**
     * Get posts created by this user
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get scholarships created by this user
     */
    public function scholarships(): HasMany
    {
        return $this->hasMany(Scholarship::class, 'created_by');
    }

    /**
     * Get organizations this user is following
     */
    public function followedOrganizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_followers')
            ->withTimestamps();
    }

    /**
     * Get groups this user belongs to
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_members')
            ->using(GroupMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active groups this user belongs to
     */
    public function activeGroups(): BelongsToMany
    {
        return $this->groups()->wherePivot('status', 'active');
    }

    /**
     * Get pending group memberships for this user
     */
    public function pendingGroups(): BelongsToMany
    {
        return $this->groups()->wherePivot('status', 'pending');
    }

    /**
     * Get groups this user created
     */
    public function createdGroups(): HasMany
    {
        return $this->hasMany(Group::class, 'created_by');
    }

    /**
     * Check if user is following an organization
     */
    public function isFollowing(Organization $organization): bool
    {
        return $this->followedOrganizations()->where('organization_id', $organization->id)->exists();
    }

    /**
     * Get comments made by this user
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get likes made by this user
     */
    public function likes(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    /**
     * Get shares made by this user
     */
    public function shares(): HasMany
    {
        return $this->hasMany(Share::class);
    }

    /**
     * Get users who are following this user
     */
    public function followers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_followers', 'followed_id', 'follower_id')
            ->withTimestamps();
    }

    /**
     * Get users this user is following
     */
    public function following(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_followers', 'follower_id', 'followed_id')
            ->withTimestamps();
    }

    /**
     * Check if this user is following another user
     */
    public function isFollowingUser(User $user): bool
    {
        return $this->following()->where('followed_id', $user->id)->exists();
    }

    /**
     * Check if this user is followed by another user
     */
    public function isFollowedByUser(User $user): bool
    {
        return $this->followers()->where('follower_id', $user->id)->exists();
    }

    /**
     * Follow another user
     */
    public function followUser(User $user): bool
    {
        if ($this->id === $user->id) {
            return false; // Can't follow yourself
        }

        if ($this->isFollowingUser($user)) {
            return false; // Already following
        }

        $this->following()->attach($user->id);

        // Send notification to the followed user
        $user->notify(new \App\Notifications\UserFollowed($this));

        return true;
    }

    /**
     * Unfollow another user
     */
    public function unfollowUser(User $user): bool
    {
        if (!$this->isFollowingUser($user)) {
            return false; // Not following
        }

        $this->following()->detach($user->id);
        return true;
    }

    /**
     * Get default notification preferences
     */
    public function getDefaultNotificationPreferences(): array
    {
        return [
            'post_reactions' => true,
            'post_comments' => true,
            'post_shares' => true,
            'share_reactions' => true,
            'share_comments' => true,
            'comment_reactions' => true,
            'comment_replies' => true,
            'user_follows' => true,
            'organization_posts' => true,
            'organization_approvals' => true,
            'group_posts' => true,
            'group_memberships' => true,
            'group_membership_requests' => true,
            'group_post_approvals' => true,
            'scholarship_updates' => true,
            'admin_notifications' => true,
        ];
    }

    /**
     * Get notification preferences with defaults
     */
    public function getNotificationPreferences(): array
    {
        return array_merge(
            $this->getDefaultNotificationPreferences(),
            $this->notification_preferences ?? []
        );
    }

    /**
     * Check if user wants to receive a specific notification type
     */
    public function wantsNotification(string $type): bool
    {
        if (!$this->notifications_enabled) {
            return false;
        }

        $preferences = $this->getNotificationPreferences();
        return $preferences[$type] ?? true;
    }

    /**
     * Update notification preference for a specific type
     */
    public function setNotificationPreference(string $type, bool $enabled): void
    {
        $preferences = $this->getNotificationPreferences();
        $preferences[$type] = $enabled;
        $this->update(['notification_preferences' => $preferences]);
    }

    /**
     * Get user avatar URL with fallback
     */
    public function getAvatarUrl(int $size = 40): string
    {
        // Always prioritize real profile picture if it exists
        if ($this->avatar) {
            return \Illuminate\Support\Facades\Storage::disk('public')->url($this->avatar);
        }

        // Fallback to generated avatar only if no real picture exists
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7BC74D&background=EEEEEE&size=' . $size;
    }

    /**
     * Check if user has a real profile picture (not generated)
     */
    public function hasRealProfilePicture(): bool
    {
        return !empty($this->avatar);
    }

    /**
     * Get profile picture URL specifically for notifications
     */
    public function getNotificationAvatarUrl(): string
    {
        // For notifications, we want to show real profile pictures when available
        if ($this->avatar) {
            return \Illuminate\Support\Facades\Storage::disk('public')->url($this->avatar);
        }

        // Only use generated avatar as last resort
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7BC74D&background=EEEEEE&size=64';
    }
}
