<?php

namespace App\Livewire;

use App\Models\Comment;
use App\Models\Share;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedCommentItem extends Component
{
    public Comment $comment;
    public Share $share;
    public $showReplies = false;
    public $showReplyForm = false;
    public $newReply = '';
    public $isEditing = false;
    public $editContent = '';

    public function mount(Comment $comment, Share $share)
    {
        $this->comment = $comment;
        $this->share = $share;
        $this->editContent = $comment->content;
    }

    public function toggleReplies()
    {
        $this->showReplies = !$this->showReplies;
        if ($this->showReplies) {
            $this->comment->load(['replies.user', 'replies.reactions']);
        }
    }

    public function toggleReplyForm()
    {
        $this->showReplyForm = !$this->showReplyForm;
        if (!$this->showReplyForm) {
            $this->newReply = '';
        }
    }

    public function addReply()
    {
        if (!Auth::check() || empty(trim($this->newReply))) {
            return;
        }

        $reply = Comment::create([
            'content' => trim($this->newReply),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => Auth::id(),
            'parent_id' => $this->comment->id,
        ]);

        $reply->load('user', 'reactions');

        $this->newReply = '';
        $this->showReplyForm = false;
        $this->showReplies = true;
        
        // Refresh the comment to get updated replies
        $this->comment->load(['replies.user', 'replies.reactions']);

        // Dispatch event to parent modal
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'commentCount' => $this->share->comments()->count(),
        ]);
    }

    public function toggleReaction($reactionType)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $existingReaction = $this->comment->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->type === $reactionType) {
                // Remove reaction if clicking the same type
                $existingReaction->delete();
            } else {
                // Update reaction type
                $existingReaction->update(['type' => $reactionType]);
            }
        } else {
            // Create new reaction
            $this->comment->reactions()->create([
                'user_id' => $user->id,
                'type' => $reactionType,
            ]);
        }

        // Refresh comment reactions
        $this->comment->load('reactions');
    }

    public function toggleReplyReaction($replyId, $reactionType)
    {
        if (!Auth::check()) {
            return;
        }

        $reply = Comment::find($replyId);
        if (!$reply) return;

        $user = Auth::user();
        $existingReaction = $reply->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->type === $reactionType) {
                $existingReaction->delete();
            } else {
                $existingReaction->update(['type' => $reactionType]);
            }
        } else {
            $reply->reactions()->create([
                'user_id' => $user->id,
                'type' => $reactionType,
            ]);
        }

        // Refresh replies with reactions
        $this->comment->load(['replies.user', 'replies.reactions']);
    }

    public function startEdit()
    {
        if (Auth::id() === $this->comment->user_id || Auth::user()->isAdmin()) {
            $this->isEditing = true;
            $this->editContent = $this->comment->content;
        }
    }

    public function saveEdit()
    {
        if (Auth::id() === $this->comment->user_id || Auth::user()->isAdmin()) {
            $this->comment->update(['content' => trim($this->editContent)]);
            $this->isEditing = false;
        }
    }

    public function cancelEdit()
    {
        $this->isEditing = false;
        $this->editContent = $this->comment->content;
    }

    public function deleteComment()
    {
        if (Auth::id() === $this->comment->user_id || Auth::user()->isAdmin()) {
            $this->comment->delete();
            
            // Dispatch event to parent modal
            $this->dispatch('commentDeleted', [
                'shareId' => $this->share->id,
                'commentCount' => $this->share->comments()->count(),
            ]);
        }
    }

    public function deleteReply($replyId)
    {
        $reply = Comment::find($replyId);
        if ($reply && (Auth::id() === $reply->user_id || Auth::user()->isAdmin())) {
            $reply->delete();
            
            // Refresh replies
            $this->comment->load(['replies.user', 'replies.reactions']);
            
            // Dispatch event to parent modal
            $this->dispatch('commentDeleted', [
                'shareId' => $this->share->id,
                'commentCount' => $this->share->comments()->count(),
            ]);
        }
    }

    public function render()
    {
        return view('livewire.shared-comment-item');
    }
}
