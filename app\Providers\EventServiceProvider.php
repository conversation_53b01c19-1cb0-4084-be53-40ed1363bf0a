<?php

namespace App\Providers;

use App\Events\PostReactionAdded;
use App\Events\PostCommentAdded;
use App\Events\PostSharedEvent;
use App\Events\CommentReactionAdded;
use App\Events\ShareReactionAdded;
use App\Listeners\SendPostReactionNotification;
use App\Listeners\SendPostCommentNotification;
use App\Listeners\SendPostShareNotification;
use App\Listeners\SendCommentReactionNotification;
use App\Listeners\SendShareReactionNotification;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        
        // Post interaction events
        PostReactionAdded::class => [
            SendPostReactionNotification::class,
        ],
        
        PostCommentAdded::class => [
            SendPostCommentNotification::class,
        ],
        
        PostSharedEvent::class => [
            SendPostShareNotification::class,
        ],

        CommentReactionAdded::class => [
            SendCommentReactionNotification::class,
        ],

        ShareReactionAdded::class => [
            SendShareReactionNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
