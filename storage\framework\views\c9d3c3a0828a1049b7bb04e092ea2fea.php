<div class="shared-comments-section bg-gray-50">
    <!-- Add Comment Form -->
    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
        <div class="p-4 border-b border-gray-100">
            <form wire:submit.prevent="addComment">
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                             src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e(auth()->user()->name); ?>">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="relative">
                            <textarea wire:model="newComment" rows="1"
                                      placeholder="Write a comment..."
                                      class="w-full bg-white text-gray-900 border-2 border-gray-300 rounded-full px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none shadow-sm"
                                      style="min-height: 44px;" 
                                      required></textarea>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 <?php echo e($newComment ? 'opacity-100' : 'opacity-0'); ?> transition-opacity duration-200">
                                <button type="submit"
                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Comments List -->
    <div class="divide-y divide-gray-100">
        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150">
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                             src="<?php echo e($comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($comment->user->name); ?>">
                    </a>
                    <div class="flex-1 min-w-0">
                        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                            <div class="flex items-center space-x-2 mb-2">
                                <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                    <?php echo e($comment->user->name); ?>

                                </a>
                                <span class="text-xs text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                <!--[if BLOCK]><![endif]--><?php if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin())): ?>
                                    <div class="relative ml-auto" x-data="{ open: false }">
                                        <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                            </svg>
                                        </button>
                                        <div x-show="open" @click.away="open = false" x-transition
                                             class="absolute right-0 top-8 w-32 bg-white shadow-lg rounded-md border border-gray-200 py-1 z-10">
                                            <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                Edit
                                            </button>
                                            <button class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50">
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            <!-- Comment Content -->
                            <div class="text-gray-900 text-sm">
                                <?php echo nl2br(e($comment->content)); ?>

                            </div>
                        </div>

                        <!-- Comment Actions -->
                        <div class="flex items-center space-x-4 mt-2 ml-4">
                            <!-- Like Button -->
                            <?php
                                $userReaction = auth()->check() ? $comment->reactions->where('user_id', auth()->id())->first() : null;
                                $likesCount = $comment->reactions->where('type', 'like')->count();
                            ?>
                            <button class="flex items-center space-x-1 text-xs <?php echo e($userReaction && $userReaction->type === 'like' ? 'text-red-600 font-medium' : 'text-gray-500'); ?> hover:text-red-600 transition-colors">
                                <svg class="w-4 h-4" fill="<?php echo e($userReaction && $userReaction->type === 'like' ? 'currentColor' : 'none'); ?>" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                                <span>Like</span>
                                <!--[if BLOCK]><![endif]--><?php if($likesCount > 0): ?>
                                    <span>(<?php echo e($likesCount); ?>)</span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </button>

                            <!-- Reply Button -->
                            <button class="text-xs text-gray-500 hover:text-gray-700 transition-colors">
                                Reply
                            </button>

                            <!-- View Replies Button -->
                            <!--[if BLOCK]><![endif]--><?php if($comment->replies && $comment->replies->count() > 0): ?>
                                <button class="flex items-center space-x-1 text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors">
                                    <svg class="w-4 h-4 transform transition-transform" viewBox="0 0 24 24">
                                        <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                                    </svg>
                                    <span>
                                        View <?php echo e($comment->replies->count()); ?> <?php echo e($comment->replies->count() === 1 ? 'reply' : 'replies'); ?>

                                    </span>
                                </button>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-gray-600 text-center py-8 px-4">
                <div class="max-w-sm mx-auto">
                    <div class="w-12 h-12 mx-auto mb-3 bg-gray-200 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <p class="text-sm text-gray-600">No comments yet. Be the first to comment!</p>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Load More Comments Button -->
    <!--[if BLOCK]><![endif]--><?php if($this->share->comments()->whereNull('parent_id')->count() > count($comments)): ?>
        <div class="p-4 text-center border-t border-gray-100">
            <button wire:click="loadMoreComments" 
                    class="text-sm text-custom-green hover:text-custom-second-darkest font-medium transition-colors">
                Load more comments
            </button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    });

    // Smooth scroll to new comments
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList.contains('comment-item')) {
                        node.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }
        });
    });

    // Observe comment lists for new additions
    const commentLists = document.querySelectorAll('.shared-comments-section .divide-y');
    commentLists.forEach(list => {
        observer.observe(list, { childList: true, subtree: true });
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-comments.blade.php ENDPATH**/ ?>