<?php

namespace App\Events;

use App\Models\User;
use App\Models\Share;
use App\Models\Reaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ShareReactionAdded
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public Share $share;
    public Reaction $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Share $share, Reaction $reaction)
    {
        $this->user = $user;
        $this->share = $share;
        $this->reaction = $reaction;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
