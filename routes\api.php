<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Share;

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Shared post API endpoints
Route::middleware(['auth:sanctum'])->get('/shares/{share}/comments/count', function (Share $share) {
    return response()->json([
        'count' => $share->comments()->count()
    ]);
});

Route::middleware(['auth:sanctum'])->get('/shares/{share}/reactions/summary', function (Share $share) {
    $reactions = $share->reactions()
        ->selectRaw('type, COUNT(*) as count')
        ->groupBy('type')
        ->pluck('count', 'type')
        ->toArray();
    
    $totalReactions = array_sum($reactions);
    
    return response()->json([
        'totalReactions' => $totalReactions,
        'reactionCounts' => $reactions
    ]);
});
