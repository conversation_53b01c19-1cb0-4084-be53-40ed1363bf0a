<!-- Shared Post Comment Modal -->
<div x-show="$wire.isOpen" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
     @click="$wire.closeModal()"
     style="display: none;">
    
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" 
         style="height: 90vh; max-height: 90vh;" 
         @click.stop>
         
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                <?php echo e($share->user->name); ?>'s shared post
            </h3>
            <button @click="$wire.closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Side: Original Post Preview -->
            <div class="w-1/2 border-r border-gray-200 flex flex-col">
                <!-- Share Message -->
                <!--[if BLOCK]><![endif]--><?php if($share->message): ?>
                    <div class="p-4 border-b border-gray-100 bg-gray-50">
                        <div class="flex items-start space-x-3">
                            <img class="h-8 w-8 rounded-full" 
                                 src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                 alt="<?php echo e($share->user->name); ?>">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900 text-sm"><?php echo e($share->user->name); ?></div>
                                <div class="text-gray-700 text-sm mt-1"><?php echo nl2br(e($share->message)); ?></div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Original Post Content -->
                <div class="flex-1 overflow-y-auto p-4">
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <!-- Original Post Header -->
                        <div class="p-3 bg-gray-50 border-b border-gray-200">
                            <div class="flex items-center space-x-2">
                                <!--[if BLOCK]><![endif]--><?php if($share->post->organization): ?>
                                    <img class="h-6 w-6 rounded-full" 
                                         src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>" 
                                         alt="<?php echo e($share->post->organization->name); ?>">
                                    <span class="font-medium text-sm text-gray-900"><?php echo e($share->post->organization->name); ?></span>
                                <?php else: ?>
                                    <img class="h-6 w-6 rounded-full" 
                                         src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                         alt="<?php echo e($share->post->user->name); ?>">
                                    <span class="font-medium text-sm text-gray-900"><?php echo e($share->post->user->name); ?></span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <span class="text-xs text-gray-500"><?php echo e($share->post->published_at->diffForHumans()); ?></span>
                            </div>
                        </div>

                        <!-- Original Post Body -->
                        <div class="p-3 bg-white">
                            <h4 class="font-semibold text-gray-900 mb-2"><?php echo e($share->post->title); ?></h4>
                            <!--[if BLOCK]><![endif]--><?php if($share->post->content): ?>
                                <p class="text-gray-700 text-sm"><?php echo e(Str::limit($share->post->content, 300)); ?></p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Post Images -->
                            <!--[if BLOCK]><![endif]--><?php if($share->post->images && count($share->post->images) > 0): ?>
                                <div class="mt-3">
                                    <!--[if BLOCK]><![endif]--><?php if(count($share->post->images) == 1): ?>
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                                             alt="Post image" 
                                             class="w-full h-32 object-cover rounded">
                                    <?php else: ?>
                                        <div class="grid grid-cols-2 gap-1">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                                     alt="Post image <?php echo e($index + 1); ?>" 
                                                     class="w-full h-20 object-cover rounded">
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side: Comments -->
            <div class="w-1/2 flex flex-col">
                <!-- Comments Header with Sort Options -->
                <div class="p-4 border-b border-gray-200 flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <h4 class="font-medium text-gray-900">Comments (<?php echo e(count($comments)); ?>)</h4>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-sm text-gray-600 hover:text-gray-800 flex items-center space-x-1">
                                <span>Sort by: <?php echo e(ucfirst(str_replace('_', ' ', $sortBy))); ?></span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-40 bg-white shadow-lg rounded-md border border-gray-200 py-1 z-10">
                                <button wire:click="changeSortOrder('newest')" @click="open = false"
                                        class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 <?php echo e($sortBy === 'newest' ? 'bg-gray-50 font-medium' : ''); ?>">
                                    Newest first
                                </button>
                                <button wire:click="changeSortOrder('oldest')" @click="open = false"
                                        class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 <?php echo e($sortBy === 'oldest' ? 'bg-gray-50 font-medium' : ''); ?>">
                                    Oldest first
                                </button>
                                <button wire:click="changeSortOrder('most_liked')" @click="open = false"
                                        class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 <?php echo e($sortBy === 'most_liked' ? 'bg-gray-50 font-medium' : ''); ?>">
                                    Most liked
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comments List -->
                <div class="flex-1 overflow-y-auto">
                    <div class="divide-y divide-gray-200">
                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-comment-item', ['comment' => $comment,'share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'comment-'.$comment->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-gray-600 text-center py-12 px-4">
                                <div class="max-w-sm mx-auto">
                                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                                    <p class="text-gray-600">Be the first to comment on this shared post!</p>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>

                <!-- Add Comment Form -->
                <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
                    <form wire:submit.prevent="addComment">
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full"
                                     src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                     alt="<?php echo e(auth()->user()->name); ?>">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="relative">
                                    <textarea wire:model="newComment" rows="1"
                                              placeholder="Write a comment..."
                                              class="w-full bg-gray-50 text-gray-900 border-2 border-gray-300 rounded-full px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none"
                                              style="min-height: 40px;" 
                                              required></textarea>
                                    <div class="absolute right-2 top-1/2 transform -translate-y-1/2 <?php echo e($newComment ? 'opacity-100' : 'opacity-0'); ?> transition-opacity duration-200">
                                        <button type="submit"
                                                class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-comment-modal.blade.php ENDPATH**/ ?>