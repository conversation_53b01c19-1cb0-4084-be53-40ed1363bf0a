<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Comment;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Events\ShareCommentAdded;

class SharedPostComments extends Component
{
    public Share $share;
    public $newComment = '';
    public $comments = [];
    public $showComments = true;

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadComments();
    }

    public function loadComments()
    {
        $this->comments = $this->share->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->whereNull('parent_id')
            ->latest()
            ->take(3) // Show only first 3 comments inline
            ->get();
    }

    public function addComment()
    {
        if (!Auth::check() || empty(trim($this->newComment))) {
            return;
        }

        $comment = Comment::create([
            'content' => trim($this->newComment),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => Auth::id(),
        ]);

        $comment->load('user', 'reactions');

        // Fire event for notifications
        event(new ShareCommentAdded(Auth::user(), $this->share, $comment));

        $this->newComment = '';
        $this->loadComments();

        // Dispatch event for real-time updates
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'commentCount' => $this->share->comments()->count(),
        ]);
    }

    public function render()
    {
        return view('livewire.shared-post-comments');
    }
}
