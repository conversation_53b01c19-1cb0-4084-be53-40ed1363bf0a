<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostReactions extends Component
{
    public Share $share;
    public $userReaction = null;
    public $reactionCounts = [];
    public $totalReactions = 0;

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadReactionData();
    }

    public function loadReactionData()
    {
        // Get current user's reaction
        if (Auth::check()) {
            $this->userReaction = $this->share->reactions()
                ->where('user_id', Auth::id())
                ->first();
        }

        // Get reaction counts
        $this->reactionCounts = $this->share->reactions()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Calculate total reactions
        $this->totalReactions = array_sum($this->reactionCounts);
    }

    public function toggleReaction($reactionType)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $existingReaction = $this->share->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->type === $reactionType) {
                // Remove reaction if clicking the same type
                $existingReaction->delete();
                $this->userReaction = null;
            } else {
                // Update reaction type
                $existingReaction->update(['type' => $reactionType]);
                $this->userReaction = $existingReaction->fresh();
            }
        } else {
            // Create new reaction
            $this->userReaction = $this->share->reactions()->create([
                'user_id' => $user->id,
                'type' => $reactionType,
            ]);
        }

        // Reload reaction data
        $this->loadReactionData();

        // Dispatch event for real-time updates
        $this->dispatch('reactionUpdated', [
            'shareId' => $this->share->id,
            'totalReactions' => $this->totalReactions,
            'reactionCounts' => $this->reactionCounts,
            'userReaction' => $this->userReaction ? $this->userReaction->type : null,
        ]);
    }

    public function render()
    {
        return view('livewire.shared-post-reactions');
    }
}
