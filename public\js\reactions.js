// Facebook-style Reaction System JavaScript

class FacebookReactionSystem {
    constructor() {
        this.hideTimeout = null;
        this.longPressTimer = null;
        this.longPressDelay = 500; // 500ms for long press
        this.isLongPress = false;
        this.reactions = [
            { type: 'like', emoji: '/assets/emojis/like.gif', label: 'Like', color: 'text-blue-600' },
            { type: 'love', emoji: '/assets/emojis/love.gif', label: 'Love', color: 'text-red-600' },
            { type: 'haha', emoji: '/assets/emojis/haha.gif', label: 'Haha', color: 'text-yellow-600' },
            { type: 'wow', emoji: '/assets/emojis/wow.gif', label: 'Wow', color: 'text-orange-600' },
            { type: 'sad', emoji: '/assets/emojis/sad.gif', label: 'Sad', color: 'text-yellow-600' },
            { type: 'angry', emoji: '/assets/emojis/angry.gif', label: 'Angry', color: 'text-red-600' }
        ];
        this.init();
    }

    init() {
        console.log('Facebook Reaction system initialized');
        this.bindEvents();
        this.createReactionPopups();
    }

    bindEvents() {
        // Handle clicks outside reaction popups to close them
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.reaction-wrapper')) {
                this.hideAllPopups();
            }
        });

        // Delegate event listeners for reaction buttons and popups
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleReactionButtonHover(e.target.closest('.reaction-btn'));
            } else if (e.target.closest('.reaction-popup')) {
                this.handlePopupHover(e.target.closest('.reaction-popup'));
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.reaction-wrapper')) {
                this.handleReactionWrapperLeave(e.target.closest('.reaction-wrapper'));
            }
        }, true);

        document.addEventListener('click', (e) => {
            const reactionBtn = e.target.closest('.reaction-btn');
            const reactionOption = e.target.closest('.reaction-option');

            if (reactionBtn) {
                e.preventDefault();
                e.stopPropagation();
                this.handleReactionButtonClick(reactionBtn);
            } else if (reactionOption) {
                e.preventDefault();
                e.stopPropagation();
                this.handleReactionOptionClick(reactionOption);
            }
        }, true); // Use capture phase to ensure we get the event first

        // Mobile long press support
        document.addEventListener('touchstart', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchStart(e, e.target.closest('.reaction-btn'));
            }
        });

        document.addEventListener('touchend', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchEnd(e, e.target.closest('.reaction-btn'));
            }
        });
    }

    createReactionPopups() {
        // Find all reaction wrappers and create popups for them
        const wrappers = document.querySelectorAll('.reaction-wrapper');
        wrappers.forEach(wrapper => {
            if (!wrapper.querySelector('.reaction-popup')) {
                this.createPopupForWrapper(wrapper);
            }
        });
    }

    createPopupForWrapper(wrapper) {
        const popup = document.createElement('div');
        popup.className = 'reaction-popup absolute bottom-full left-0 mb-2 bg-white rounded-full shadow-lg border flex items-center opacity-0 invisible transform scale-95 transition-all duration-200 z-[9999]';

        const reactionBtn = wrapper.querySelector('.reaction-btn');

        this.reactions.forEach(reaction => {
            const option = document.createElement('button');
            option.className = 'reaction-option flex flex-col items-center p-2 rounded-full transition-all duration-200 transform hover:scale-110';
            option.dataset.reactionType = reaction.type;
            option.dataset.targetId = reactionBtn.dataset.targetId;
            option.dataset.targetType = reactionBtn.dataset.targetType;

            option.innerHTML = `
                <img src="${reaction.emoji}" alt="${reaction.label}" class="reaction-emoji"
                     onerror="this.style.display='none';">
            `;

            // Let the main event delegation handle the clicks

            popup.appendChild(option);
        });

        wrapper.appendChild(popup);
    }

    handleReactionButtonHover(button) {
        const wrapper = button.closest('.reaction-wrapper');
        const popup = wrapper.querySelector('.reaction-popup');

        // If no popup exists, create it first
        if (!popup) {
            this.createPopupForWrapper(wrapper);
        }

        clearTimeout(this.hideTimeout);

        // Show popup after a short delay
        this.showTimeout = setTimeout(() => {
            if (wrapper.matches(':hover')) {
                const currentPopup = wrapper.querySelector('.reaction-popup');
                this.showPopup(currentPopup);
            }
        }, 300);
    }

    handlePopupHover(popup) {
        // Keep popup visible when hovering over it
        clearTimeout(this.hideTimeout);
    }

    handleReactionWrapperLeave(wrapper) {
        const popup = wrapper.querySelector('.reaction-popup');

        // Add a small delay to allow moving from button to popup
        setTimeout(() => {
            // Check if mouse is still over the wrapper or popup
            // Only check popup hover if popup exists
            if (!wrapper.matches(':hover') && (!popup || !popup.matches(':hover'))) {
                this.hidePopupWithDelay(popup);
            }
        }, 100);
    }

    handleReactionButtonClick(button) {
        if (this.isLongPress) {
            this.isLongPress = false;
            return;
        }

        // Quick click - toggle like
        this.toggleDefaultReaction(button);
    }

    handleReactionOptionClick(option) {
        this.selectReaction(option);
    }

    handleTouchStart(e, button) {
        this.isLongPress = false;
        this.longPressTimer = setTimeout(() => {
            this.isLongPress = true;
            const wrapper = button.closest('.reaction-wrapper');
            const popup = wrapper.querySelector('.reaction-popup');
            this.showPopup(popup);

            // Haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }, this.longPressDelay);
    }

    handleTouchEnd(e, button) {
        clearTimeout(this.longPressTimer);

        if (!this.isLongPress) {
            // Short tap - toggle like
            setTimeout(() => {
                if (!this.isLongPress) {
                    this.toggleDefaultReaction(button);
                }
            }, 50);
        }
    }

    showPopup(popup) {
        if (!popup) return;

        clearTimeout(this.hideTimeout);
        this.hideAllPopups();

        // Smart positioning to avoid viewport cutoff
        this.adjustPopupPosition(popup);

        popup.classList.remove('opacity-0', 'invisible', 'scale-95');
        popup.classList.add('opacity-100', 'visible', 'scale-100');
    }

    adjustPopupPosition(popup) {
        if (!popup) return;

        const wrapper = popup.closest('.reaction-wrapper');
        if (!wrapper) return;

        const wrapperRect = wrapper.getBoundingClientRect();
        const popupHeight = 80; // Approximate popup height
        const viewportHeight = window.innerHeight;

        // Check if popup would be cut off at the top
        if (wrapperRect.top - popupHeight < 20) {
            // Position below the button instead
            popup.classList.add('position-top');
            popup.style.bottom = 'auto';
            popup.style.top = '100%';
            popup.style.marginBottom = '0';
            popup.style.marginTop = '0.5rem';
        } else {
            // Position above the button (default)
            popup.classList.remove('position-top');
            popup.style.bottom = '100%';
            popup.style.top = 'auto';
            popup.style.marginBottom = '0.5rem';
            popup.style.marginTop = '0';
        }
    }

    hidePopupWithDelay(popup) {
        if (!popup) return;

        this.hideTimeout = setTimeout(() => {
            // Double-check that mouse is not over wrapper or popup before hiding
            const wrapper = popup.closest('.reaction-wrapper');
            if (wrapper && !wrapper.matches(':hover') && !popup.matches(':hover')) {
                this.hidePopup(popup);
            }
        }, 300);
    }

    hidePopup(popup) {
        if (!popup) return;

        popup.classList.remove('opacity-100', 'visible', 'scale-100');
        popup.classList.add('opacity-0', 'invisible', 'scale-95');
    }

    hideAllPopups() {
        const popups = document.querySelectorAll('.reaction-popup');
        popups.forEach(popup => this.hidePopup(popup));
    }

    async toggleDefaultReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;

        console.log('Toggle default reaction:', { targetId, targetType, currentReaction });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: currentReaction ? null : 'like' // Toggle off if exists, otherwise set to like
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateReactionButton(button, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);

                // Trigger real-time summary update for posts
                if (targetType === 'post' && window.postSummaryUpdater) {
                    window.postSummaryUpdater.onReactionChange(targetId);
                }

                // Sync reaction state between modal and main feed
                if (targetType === 'post') {
                    // If we're in a modal, sync to main feed
                    if (button.closest('[id^="commentModal-"]')) {
                        const modalId = button.closest('[id^="commentModal-"]').id;
                        const postId = modalId.replace('commentModal-', '');
                        if (window.syncMainFeedReactionWithModal) {
                            window.syncMainFeedReactionWithModal(postId);
                        }
                    }
                    // If we're in main feed, sync to modal (if open)
                    else if (button.closest('[data-post-id]')) {
                        const postId = button.closest('[data-post-id]').dataset.postId;
                        if (window.syncModalReactionWithMainFeed) {
                            window.syncModalReactionWithMainFeed(postId);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    }

    async selectReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const reactionType = button.dataset.reactionType;

        console.log('Select reaction:', { targetId, targetType, reactionType });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: reactionType
                })
            });

            const data = await response.json();

            if (data.success) {
                // Find the main reaction button
                const mainButton = button.closest('.reaction-wrapper').querySelector('.reaction-btn');
                this.updateReactionButton(mainButton, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);

                // Trigger real-time summary update for posts
                if (targetType === 'post' && window.postSummaryUpdater) {
                    window.postSummaryUpdater.onReactionChange(targetId);
                }

                // Sync reaction state between modal and main feed
                if (targetType === 'post') {
                    // If we're in a modal, sync to main feed
                    if (button.closest('[id^="commentModal-"]')) {
                        const modalId = button.closest('[id^="commentModal-"]').id;
                        const postId = modalId.replace('commentModal-', '');
                        if (window.syncMainFeedReactionWithModal) {
                            window.syncMainFeedReactionWithModal(postId);
                        }
                    }
                    // If we're in main feed, sync to modal (if open)
                    else if (button.closest('[data-post-id]')) {
                        const postId = button.closest('[data-post-id]').dataset.postId;
                        if (window.syncModalReactionWithMainFeed) {
                            window.syncModalReactionWithMainFeed(postId);
                        }
                    }
                }

                // Hide the popup
                const popup = button.closest('.reaction-popup');
                this.hidePopup(popup);
            }
        } catch (error) {
            console.error('Error selecting reaction:', error);
        }
    }

    updateReactionButton(button, reaction) {
        if (!button) return;

        const wrapper = button.closest('.reaction-wrapper');
        if (!wrapper) return;

        if (reaction) {
            // Update button to show the reaction
            button.innerHTML = `
                <img src="${reaction.emoji}" alt="${reaction.label}" class="w-5 h-5 reaction-emoji"
                     onerror="this.style.display='none';">
                <span class="text-sm font-medium">${reaction.label}</span>
            `;
            button.className = `reaction-btn flex items-center space-x-2 ${reaction.color} transition-colors duration-200 py-2 px-3 rounded-lg `;
            button.dataset.currentReaction = reaction.type;
        } else {
            // Reset button to default state
            button.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                </svg>
                <span class="text-sm font-medium">Like</span>
            `;
            button.className = 'reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg ';
            button.dataset.currentReaction = '';
        }
    }

    updateReactionCounts(targetId, targetType, counts) {
        console.log('Update reaction counts:', { targetId, targetType, counts });

        // Update reaction count displays for comments
        if (targetType === 'comment') {
            const reactionWrappers = document.querySelectorAll(`[data-target-id="${targetId}"][data-target-type="comment"]`);

            reactionWrappers.forEach(wrapper => {
                const reactionWrapper = wrapper.closest('.reaction-wrapper');
                if (!reactionWrapper) return;

                // Remove existing count display
                const existingCountDisplay = reactionWrapper.querySelector('.reaction-count-display');
                if (existingCountDisplay) {
                    existingCountDisplay.remove();
                }

                // Add new count display if there are reactions
                if (counts.total > 0) {
                    // Sort reactions by count (descending) and show top 3
                    const sortedReactions = Object.entries(counts.by_type)
                        .sort(([,a], [,b]) => b.count - a.count)
                        .slice(0, 3);

                    const reactionEmojis = sortedReactions.map(([type, data]) => {
                        return `<img src="${data.emoji}" alt="${data.label}" class="w-4 h-4" onerror="this.style.display='none';">`;
                    }).join('');

                    const countHTML = `
                        <div class="reaction-count-display flex items-center space-x-1 ml-2 text-xs text-gray-600">
                            <div class="flex items-center space-x-1">
                                ${reactionEmojis}
                                <span class="text-gray-600">${counts.total}</span>
                            </div>
                        </div>
                    `;

                    reactionWrapper.insertAdjacentHTML('beforeend', countHTML);
                }
            });
        }

        // Update other count elements (for backward compatibility)
        const countElements = document.querySelectorAll(`[data-target-id="${targetId}"] .reaction-count`);
        countElements.forEach(element => {
            if (counts.total > 0) {
                element.textContent = counts.total;
                element.style.display = 'inline';
            } else {
                element.style.display = 'none';
            }
        });
    }

    /**
     * Generate Facebook-style reaction HTML for JavaScript templates
     */
    static generateReactionHTML(targetId, targetType, userReaction = null, reactionCounts = null, showCount = false) {
        const reactions = [
            { type: 'like', emoji: '/assets/emojis/like.gif', label: 'Like', color: 'text-blue-600' },
            { type: 'love', emoji: '/assets/emojis/love.gif', label: 'Love', color: 'text-red-600' },
            { type: 'haha', emoji: '/assets/emojis/haha.gif', label: 'Haha', color: 'text-yellow-600' },
            { type: 'wow', emoji: '/assets/emojis/wow.gif', label: 'Wow', color: 'text-orange-600' },
            { type: 'sad', emoji: '/assets/emojis/sad.gif', label: 'Sad', color: 'text-yellow-600' },
            { type: 'angry', emoji: '/assets/emojis/angry.gif', label: 'Angry', color: 'text-red-600' }
        ];

        const currentReaction = userReaction ? reactions.find(r => r.type === userReaction.type) : null;
        const buttonColor = currentReaction ? currentReaction.color : 'text-gray-500';
        const buttonText = currentReaction ? currentReaction.label : 'Like';
        const buttonIcon = currentReaction
            ? `<img src="${currentReaction.emoji}" alt="${currentReaction.label}" class="w-4 h-4 reaction-emoji">`
            : `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
               </svg>`;

        // Generate reaction count display
        let reactionCountHTML = '';

        if (showCount && reactionCounts && Object.keys(reactionCounts).length > 0) {
            const totalReactions = Object.values(reactionCounts).reduce((sum, count) => sum + count, 0);

            if (totalReactions > 0) {
                // Sort reactions by count (descending) and show top 3
                const sortedReactions = Object.entries(reactionCounts)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3);

                const reactionEmojis = sortedReactions.map(([type, count]) => {
                    const reaction = reactions.find(r => r.type === type);
                    return reaction ? `<img src="${reaction.emoji}" alt="${reaction.label}" class="w-4 h-4" onerror="this.style.display='none';">` : '';
                }).join('');

                reactionCountHTML = `
                    <div class="reaction-count-display flex items-center space-x-1 ml-2 text-xs text-gray-600">
                        <div class="flex items-center space-x-1">
                            ${reactionEmojis}
                            <span class="text-gray-600">${totalReactions}</span>
                        </div>
                    </div>
                `;
            }
        }

        return `
            <div class="reaction-wrapper relative inline-block">
                <button class="reaction-btn flex items-center space-x-1 ${buttonColor} transition-colors duration-200 hover:bg-gray-100 px-2 py-1 rounded-lg text-sm"
                        data-target-id="${targetId}"
                        data-target-type="${targetType}"
                        data-current-reaction="${userReaction ? userReaction.type : ''}">
                    ${buttonIcon}
                    <span class="font-medium">${buttonText}</span>
                </button>
                ${reactionCountHTML}
            </div>
        `;
    }
}

// Initialize the reaction system when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.facebookReactionSystem = new FacebookReactionSystem();
});

// Make it globally available
window.FacebookReactionSystem = FacebookReactionSystem;