<div class="shared-comments-section bg-white">
    <!-- Comment Form -->
    @auth
        <div class="p-4 border-b border-gray-50">
            <form wire:submit.prevent="addComment" class="flex space-x-3">
                <div class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                         src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                         alt="{{ auth()->user()->name }}">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="relative">
                        <textarea wire:model="newComment" 
                                  rows="1"
                                  placeholder="Write a comment..."
                                  class="block w-full resize-none border-0 bg-gray-100 rounded-full py-3 px-4 text-sm placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:bg-white transition-colors"
                                  style="min-height: 44px; max-height: 120px;"
                                  x-data="{ resize: () => { $el.style.height = 'auto'; $el.style.height = Math.min($el.scrollHeight, 120) + 'px'; } }"
                                  x-init="resize()"
                                  @input="resize()"
                                  @keydown.enter.prevent="if (!$event.shiftKey && $wire.newComment.trim()) { $wire.addComment(); }"></textarea>
                        
                        @if($newComment)
                            <button type="submit" 
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-700 p-1">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    @endauth

    <!-- Comments List -->
    <div class="divide-y divide-gray-100">
        @forelse($comments as $comment)
            <livewire:shared-comment-item :comment="$comment" :share="$share" :key="'inline-comment-'.$comment->id" />
        @empty
            <div class="p-4 text-center text-gray-500">
                <p class="text-sm">No comments yet. Be the first to comment!</p>
            </div>
        @endforelse
    </div>

    <!-- View More Comments Link -->
    @if($share->comments()->count() > 3)
        <div class="p-4 border-t border-gray-100">
            <button onclick="openSharedCommentModal({{ $share->id }})" 
                    class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                View all {{ $share->comments()->count() }} comments
            </button>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    });

    // Smooth scroll to new comments
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList.contains('comment-item')) {
                        node.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }
        });
    });

    // Observe comment lists for new additions
    const commentLists = document.querySelectorAll('.shared-comments-section .divide-y');
    commentLists.forEach(list => {
        observer.observe(list, { childList: true, subtree: true });
    });
});
</script>
