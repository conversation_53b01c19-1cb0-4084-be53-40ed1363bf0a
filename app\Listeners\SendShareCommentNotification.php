<?php

namespace App\Listeners;

use App\Events\ShareCommentAdded;
use App\Notifications\ShareCommented;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendShareCommentNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(ShareCommentAdded $event): void
    {
        // Don't notify if user commented on their own share
        if ($event->user->id === $event->share->user_id) {
            return;
        }

        // Check if the share owner wants to receive this type of notification
        if (!$event->share->user->wantsNotification('share_comments')) {
            return;
        }

        // Send notification to the share owner
        $event->share->user->notify(new ShareCommented($event->user, $event->share, $event->comment));
    }
}
