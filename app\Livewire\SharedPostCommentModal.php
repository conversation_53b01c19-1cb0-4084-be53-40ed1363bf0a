<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Comment;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Events\PostCommentAdded;

class SharedPostCommentModal extends Component
{
    public Share $share;
    public $isOpen = false;
    public $newComment = '';
    public $comments = [];
    public $sortBy = 'newest';

    protected $listeners = [
        'openCommentModal' => 'openModal',
        'closeCommentModal' => 'closeModal',
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadComments();
    }

    public function openModal($data = null)
    {
        if ($data && isset($data['shareId']) && $data['shareId'] == $this->share->id) {
            $this->isOpen = true;
            $this->loadComments();
        }
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->newComment = '';
    }

    public function loadComments()
    {
        $query = $this->share->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->whereNull('parent_id');

        if ($this->sortBy === 'newest') {
            $query->latest();
        } else {
            $query->oldest();
        }

        $this->comments = $query->get();
    }

    public function addComment()
    {
        if (!Auth::check() || empty(trim($this->newComment))) {
            return;
        }

        $comment = Comment::create([
            'content' => trim($this->newComment),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => Auth::id(),
        ]);

        $comment->load('user', 'reactions');

        // Fire event for notifications
        event(new PostCommentAdded(Auth::user(), $this->share, $comment));

        $this->newComment = '';
        $this->loadComments();

        // Dispatch event for real-time updates
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'commentCount' => $this->share->comments()->count(),
        ]);
    }

    public function addReply($parentId, $content)
    {
        if (!Auth::check() || empty(trim($content))) {
            return;
        }

        $reply = Comment::create([
            'content' => trim($content),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => Auth::id(),
            'parent_id' => $parentId,
        ]);

        $reply->load('user', 'reactions');

        $this->loadComments();

        // Dispatch event for real-time updates
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'commentCount' => $this->share->comments()->count(),
        ]);
    }

    public function deleteComment($commentId)
    {
        $comment = Comment::find($commentId);
        
        if ($comment && (Auth::id() === $comment->user_id || Auth::user()->isAdmin())) {
            $comment->delete();
            $this->loadComments();

            // Dispatch event for real-time updates
            $this->dispatch('commentDeleted', [
                'shareId' => $this->share->id,
                'commentCount' => $this->share->comments()->count(),
            ]);
        }
    }

    public function updateSort($sortBy)
    {
        $this->sortBy = $sortBy;
        $this->loadComments();
    }

    public function render()
    {
        return view('livewire.shared-post-comment-modal');
    }
}
