<div class="reaction-wrapper relative inline-block" x-data="{ showReactionPopup: false }">
    <!-- Main Reaction Button -->
    <button class="reaction-btn flex items-center space-x-2 <?php echo e($userReaction ? \App\Models\Reaction::getReactionDetails($userReaction->type)['color'] : 'text-gray-500'); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
            wire:click="toggleReaction('<?php echo e($userReaction ? $userReaction->type : 'like'); ?>')"
            @mouseenter="showReactionPopup = true"
            @mouseleave="showReactionPopup = false"
            >
        <!--[if BLOCK]><![endif]--><?php if($userReaction): ?>
            <?php $details = \App\Models\Reaction::getReactionDetails($userReaction->type); ?>
            <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-5 h-5">
            <span class="font-medium"><?php echo e($details['label']); ?></span>
        <?php else: ?>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            <span class="font-medium">Like</span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </button>

    <!-- Reaction Count Display -->
    <!--[if BLOCK]><![endif]--><?php if($totalReactions > 0): ?>
        <span class="text-sm text-gray-600 ml-2"><?php echo e($totalReactions); ?></span>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Reaction Popup -->
    <div x-show="showReactionPopup" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="reaction-popup absolute bottom-full left-0 mb-2 bg-white rounded-full shadow-lg border border-gray-200 px-3 py-2 flex space-x-2 z-50"
         style="display: none;">
        
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = ['like', 'love', 'haha', 'wow', 'sad', 'angry']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reactionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $details = \App\Models\Reaction::getReactionDetails($reactionType);
            ?>
            <button wire:click="toggleReaction('<?php echo e($reactionType); ?>')"
                    class="reaction-option hover:scale-125 transform transition-transform duration-150 p-1 rounded-full hover:bg-gray-100"
                    title="<?php echo e($details['label']); ?>">
                <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-8 h-8">
            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <style>
    /* Reaction popup positioning and styling */
    .reaction-popup {
        min-width: 280px;
        white-space: nowrap;
    }

    .reaction-option {
        position: relative;
        transition: all 0.2s ease;
    }

    .reaction-option:hover {
        transform: scale(1.2);
    }

    /* Mobile touch improvements */
    @media (max-width: 768px) {
        .reaction-popup {
            bottom: 120%;
            padding: 8px 12px;
        }

        .reaction-option {
            padding: 8px;
        }

        .reaction-option img {
            width: 32px;
            height: 32px;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .reaction-popup {
            background-color: #374151;
            border-color: #4B5563;
        }

        .reaction-option:hover {
            background-color: #4B5563;
        }
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for reaction updates
        Livewire.on('reactionUpdated', (data) => {
            // Update summary bars if they exist
            if (window.postSummaryUpdater) {
                window.postSummaryUpdater.updateShareReactionSummary(data.shareId, {
                    total: data.totalReactions,
                    counts: data.reactionCounts
                });
            }
        });
    });
    </script>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-reactions.blade.php ENDPATH**/ ?>