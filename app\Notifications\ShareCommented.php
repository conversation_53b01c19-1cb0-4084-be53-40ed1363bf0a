<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ShareCommented extends Notification implements ShouldQueue
{
    use Queueable;

    public User $user;
    public Share $share;
    public Comment $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Share $share, Comment $comment)
    {
        $this->user = $user;
        $this->share = $share;
        $this->comment = $comment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'share_comment',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->avatar,
            'share_id' => $this->share->id,
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'message' => $this->user->name . ' commented on your shared post',
            'url' => route('posts.show', $this->share->post_id),
        ];
    }
}
