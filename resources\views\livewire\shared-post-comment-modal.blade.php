<!-- Shared Post Comment Modal -->
@if($isOpen)
<div x-data
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
     wire:click="closeModal">
    
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden"
         style="height: 90vh; max-height: 90vh;"
         @click.stop>
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                {{ $share->user->name }}'s shared post
            </h3>
            <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Shared Post Content -->
        <div class="p-4 border-b border-gray-200 flex-shrink-0">
            <!-- Share Header -->
            <div class="flex items-center space-x-3 mb-3">
                <a href="{{ route('profile.user', $share->user) }}">
                    <img class="h-10 w-10 rounded-full" 
                         src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                         alt="{{ $share->user->name }}">
                </a>
                <div>
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('profile.user', $share->user) }}" class="font-semibold text-gray-900 hover:text-custom-green">
                            {{ $share->user->name }}
                        </a>
                        <span class="text-gray-500">shared a post</span>
                    </div>
                    <p class="text-sm text-gray-500">{{ $share->created_at->diffForHumans() }}</p>
                </div>
            </div>

            <!-- Share Message -->
            @if($share->message)
                <div class="mb-4">
                    <p class="text-gray-800">{!! nl2br(e($share->message)) !!}</p>
                </div>
            @endif

            <!-- Original Post Preview -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div class="flex items-center space-x-3 mb-3">
                    <a href="{{ route('profile.user', $share->post->user) }}">
                        <img class="h-8 w-8 rounded-full" 
                             src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                             alt="{{ $share->post->user->name }}">
                    </a>
                    <div>
                        <a href="{{ route('profile.user', $share->post->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm">
                            {{ $share->post->user->name }}
                        </a>
                        <p class="text-xs text-gray-500">{{ $share->post->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                <div class="text-gray-800 text-sm">
                    {!! nl2br(e($share->post->content)) !!}
                </div>
            </div>
        </div>

        <!-- Reaction Summary Bar -->
        <div class="px-4 py-2 border-b border-gray-200 flex-shrink-0">
            <div class="flex items-center justify-between text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    @if($share->reactions()->count() > 0)
                        <div class="flex items-center space-x-1">
                            @php
                                $reactionCounts = $share->reactions()
                                    ->selectRaw('type, COUNT(*) as count')
                                    ->groupBy('type')
                                    ->pluck('count', 'type')
                                    ->toArray();
                                $totalReactions = array_sum($reactionCounts);
                            @endphp
                            
                            @foreach(['like', 'love', 'haha', 'wow', 'sad', 'angry'] as $type)
                                @if(isset($reactionCounts[$type]) && $reactionCounts[$type] > 0)
                                    @php $details = \App\Models\Reaction::getReactionDetails($type); @endphp
                                    <img src="{{ $details['emoji'] }}" alt="{{ $details['label'] }}" class="w-4 h-4">
                                @endif
                            @endforeach
                            
                            <span>{{ $totalReactions }}</span>
                        </div>
                    @endif
                </div>
                
                <div class="flex items-center space-x-4">
                    @if($share->comments()->count() > 0)
                        <span>{{ $share->comments()->count() }} comment{{ $share->comments()->count() !== 1 ? 's' : '' }}</span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="px-4 py-3 border-b border-gray-200 flex-shrink-0">
            <div class="flex items-center justify-around">
                <!-- Reaction Button -->
                <livewire:shared-post-reactions :share="$share" :key="'modal-reactions-'.$share->id" />

                <button class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="font-medium">Comment</span>
                </button>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="flex-1 overflow-y-auto">
            <!-- Sort Options -->
            @if(count($comments) > 1)
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Sort by:</span>
                        <button wire:click="updateSort('newest')" 
                                class="text-sm {{ $sortBy === 'newest' ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600' }}">
                            Newest first
                        </button>
                        <button wire:click="updateSort('oldest')" 
                                class="text-sm {{ $sortBy === 'oldest' ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600' }}">
                            Oldest first
                        </button>
                    </div>
                </div>
            @endif

            <!-- Comments List -->
            <div class="divide-y divide-gray-100">
                @forelse($comments as $comment)
                    <livewire:shared-comment-item :comment="$comment" :share="$share" :key="'comment-'.$comment->id" />
                @empty
                    <div class="p-8 text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <p class="text-lg font-medium mb-2">No comments yet</p>
                        <p>Be the first to comment on this shared post!</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Add Comment Form -->
        @auth
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
            <form wire:submit.prevent="addComment" class="flex space-x-3">
                <div class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full"
                         src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                         alt="{{ auth()->user()->name }}">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="relative">
                        <textarea wire:model="newComment" 
                                  rows="1"
                                  placeholder="Write a comment..."
                                  class="block w-full resize-none border-0 bg-gray-100 rounded-full py-3 px-4 text-sm placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:bg-white transition-colors"
                                  style="min-height: 44px; max-height: 120px;"
                                  x-data="{ resize: () => { $el.style.height = 'auto'; $el.style.height = Math.min($el.scrollHeight, 120) + 'px'; } }"
                                  x-init="resize()"
                                  @input="resize()"
                                  @keydown.enter.prevent="if (!$event.shiftKey && $wire.newComment.trim()) { $wire.addComment(); }"></textarea>
                        
                        @if($newComment)
                            <button type="submit" 
                                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-700 p-1">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                        @endif
                    </div>
                </div>
            </form>
        </div>
        @endauth
    </div>
</div>
@endif
