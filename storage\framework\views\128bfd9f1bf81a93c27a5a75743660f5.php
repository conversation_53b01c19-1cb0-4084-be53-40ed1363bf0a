<div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150">
    <div class="flex space-x-3">
        <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="flex-shrink-0">
            <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                 src="<?php echo e($comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                 alt="<?php echo e($comment->user->name); ?>">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-2 mb-2">
                    <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                        <?php echo e($comment->user->name); ?>

                    </a>
                    <span class="text-xs text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                    <!--[if BLOCK]><![endif]--><?php if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin())): ?>
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg py-1 z-10 border">
                                <button wire:click="startEdit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</button>
                                <button wire:click="deleteComment" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100" onclick="return confirm('Are you sure you want to delete this comment?')">Delete</button>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                
                <!-- Comment Content -->
                <!--[if BLOCK]><![endif]--><?php if($isEditing): ?>
                    <div class="space-y-2">
                        <textarea wire:model="editContent" 
                                  class="w-full p-2 border border-gray-300 rounded-lg resize-none text-sm"
                                  rows="2"></textarea>
                        <div class="flex space-x-2">
                            <button wire:click="saveEdit" class="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700">Save</button>
                            <button wire:click="cancelEdit" class="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded-lg hover:bg-gray-400">Cancel</button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-gray-800 text-sm">
                        <?php echo nl2br(e($comment->content)); ?>

                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Comment Actions -->
            <div class="flex items-center space-x-4 mt-2 ml-4">
                <!-- Like Button -->
                <?php
                    $userReaction = auth()->check() ? $comment->reactions()->where('user_id', auth()->id())->first() : null;
                    $reactionCounts = $comment->reactions()->selectRaw('type, COUNT(*) as count')->groupBy('type')->pluck('count', 'type')->toArray();
                    $totalReactions = array_sum($reactionCounts);
                ?>
                
                <div class="flex items-center space-x-1">
                    <button wire:click="toggleReaction('like')" 
                            class="text-xs <?php echo e($userReaction && $userReaction->type === 'like' ? 'text-blue-600 font-semibold' : 'text-gray-500 hover:text-blue-600'); ?> transition-colors">
                        <?php echo e($userReaction && $userReaction->type === 'like' ? 'Liked' : 'Like'); ?>

                    </button>
                    <!--[if BLOCK]><![endif]--><?php if($totalReactions > 0): ?>
                        <span class="text-xs text-gray-500"><?php echo e($totalReactions); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Reply Button -->
                <button wire:click="toggleReplyForm" class="text-xs text-gray-500 hover:text-blue-600 transition-colors">
                    Reply
                </button>

                <!-- Show Replies Button -->
                <!--[if BLOCK]><![endif]--><?php if($comment->replies()->count() > 0): ?>
                    <button wire:click="toggleReplies" class="text-xs text-gray-500 hover:text-blue-600 transition-colors">
                        <?php echo e($showReplies ? 'Hide' : 'Show'); ?> <?php echo e($comment->replies()->count()); ?> repl<?php echo e($comment->replies()->count() === 1 ? 'y' : 'ies'); ?>

                    </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Reply Form -->
            <!--[if BLOCK]><![endif]--><?php if($showReplyForm): ?>
                <div class="mt-3 ml-4">
                    <form wire:submit.prevent="addReply" class="flex space-x-3">
                        <div class="flex-shrink-0">
                            <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                 src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name ?? 'User') . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e(auth()->user()->name ?? 'User'); ?>">
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="relative">
                                <textarea wire:model="newReply" 
                                          rows="1"
                                          placeholder="Write a reply..."
                                          class="block w-full resize-none border-0 bg-gray-100 rounded-full py-2 px-4 text-sm placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:bg-white transition-colors"
                                          style="min-height: 36px; max-height: 100px;"
                                          x-data="{ resize: () => { $el.style.height = 'auto'; $el.style.height = Math.min($el.scrollHeight, 100) + 'px'; } }"
                                          x-init="resize()"
                                          @input="resize()"
                                          @keydown.enter.prevent="if (!$event.shiftKey && $wire.newReply.trim()) { $wire.addReply(); }"></textarea>
                                
                                <!--[if BLOCK]><![endif]--><?php if($newReply): ?>
                                    <button type="submit" 
                                            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-700 p-1">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                        </svg>
                                    </button>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </form>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Replies Section -->
            <!--[if BLOCK]><![endif]--><?php if($showReplies && $comment->replies()->count() > 0): ?>
                <div class="mt-4 ml-4 space-y-3">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex space-x-3">
                            <a href="<?php echo e(route('profile.user', $reply->user)); ?>" class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                     src="<?php echo e($reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                     alt="<?php echo e($reply->user->name); ?>">
                            </a>
                            <div class="flex-1 min-w-0">
                                <div class="bg-gray-50/50 rounded-xl p-3 shadow-sm border border-gray-100">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <a href="<?php echo e(route('profile.user', $reply->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green text-xs hover:underline">
                                            <?php echo e($reply->user->name); ?>

                                        </a>
                                        <span class="text-xs text-gray-500"><?php echo e($reply->created_at->diffForHumans()); ?></span>
                                        <?php if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin())): ?>
                                            <div class="relative ml-auto" x-data="{ open: false }">
                                                <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                                    </svg>
                                                </button>
                                                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg py-1 z-10 border">
                                                    <button wire:click="deleteReply(<?php echo e($reply->id); ?>)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100" onclick="return confirm('Are you sure you want to delete this reply?')">Delete</button>
                                                </div>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    <div class="text-gray-800 text-xs">
                                        <?php echo nl2br(e($reply->content)); ?>

                                    </div>
                                </div>

                                <!-- Reply Actions -->
                                <div class="flex items-center space-x-3 mt-1 ml-3">
                                    <?php
                                        $replyUserReaction = auth()->check() ? $reply->reactions()->where('user_id', auth()->id())->first() : null;
                                        $replyReactionCounts = $reply->reactions()->selectRaw('type, COUNT(*) as count')->groupBy('type')->pluck('count', 'type')->toArray();
                                        $replyTotalReactions = array_sum($replyReactionCounts);
                                    ?>

                                    <div class="flex items-center space-x-1">
                                        <button wire:click="toggleReplyReaction(<?php echo e($reply->id); ?>, 'like')"
                                                class="text-xs <?php echo e($replyUserReaction && $replyUserReaction->type === 'like' ? 'text-blue-600 font-semibold' : 'text-gray-500 hover:text-blue-600'); ?> transition-colors">
                                            <?php echo e($replyUserReaction && $replyUserReaction->type === 'like' ? 'Liked' : 'Like'); ?>

                                        </button>
                                        <!--[if BLOCK]><![endif]--><?php if($replyTotalReactions > 0): ?>
                                            <span class="text-xs text-gray-500"><?php echo e($replyTotalReactions); ?></span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-comment-item.blade.php ENDPATH**/ ?>