<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Reaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ShareReacted extends Notification implements ShouldQueue
{
    use Queueable;

    public User $user;
    public Share $share;
    public Reaction $reaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Share $share, Reaction $reaction)
    {
        $this->user = $user;
        $this->share = $share;
        $this->reaction = $reaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $reactionDetails = Reaction::getReactionDetails($this->reaction->type);
        
        return [
            'type' => 'share_reaction',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->avatar,
            'share_id' => $this->share->id,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $reactionDetails['emoji'],
            'reaction_label' => $reactionDetails['label'],
            'message' => $this->user->name . ' reacted to your shared post with ' . $reactionDetails['label'],
            'url' => route('posts.show', $this->share->post_id),
        ];
    }
}
